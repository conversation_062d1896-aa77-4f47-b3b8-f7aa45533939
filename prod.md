```mermaid
%%{init: {"theme": "base", "themeVariables": {"background": "#ffffff", "primaryColor": "#ffffff"}}}%%
flowchart TD
    subgraph "AWS Cloud"
        subgraph "VPC: NeoX-Production"
            subgraph "Public Subnet"
                %% 网络入口节点
                Internet([Internet])
                IGW[Internet Gateway]
            end

            subgraph "Private Subnet A (Application Tier)"
                %% 应用服务节点
                RecognizePool["Medical Recognize Pool<br/>(c5.xlarge, c5.2xlarge, c5.4xlarge)"]
                MoairPool["Medical Moair Pool<br/>(c5.xlarge)"]
                ApiPool["Medical API-Only Pool<br/>(c5.xlarge)"]
            end

            subgraph "Private Subnet B (Infrastructure & Data Tier)"
                %% 基础设施节点
                DevopsAnsible["devops-ansible-01<br/>(c5.xlarge)"]
                GenericOcr["generic-ocr-ci-01<br/>(c5.xlarge)"]
                %% 数据服务节点
                MeiliSearch[("medical-meilisearch01<br/>(c5.xlarge)")]
                Grafana[("medical-grafana<br/>(c5.xlarge)")]
                LogServer[("medical-pro-generic-log<br/>(c5.xlarge)")]
            end

            subgraph "Private Subnet C (Specialized Compute)"
                %% GPU计算节点
                GpuEngine["gpu_engine<br/>(g5.xlarge)"]
            end

            subgraph "Bastion Host"
                %% 跳板机节点
                Bastion(["bastion<br/>(c5.4xlarge)"])
            end

            %% 网络连接关系
            Internet --> IGW
            IGW --> Bastion

            %% 跳板机访问关系
            Bastion --> RecognizePool
            Bastion --> MoairPool
            Bastion --> ApiPool
            Bastion --> DevopsAnsible
            Bastion --> GpuEngine

            %% 内部服务关系
            RecognizePool --> GpuEngine
        end
    end

    %% 样式类定义
    classDef network fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef compute fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef data fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef gpu fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef bastion fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c

    %% 应用样式类
    class Internet,IGW network
    class RecognizePool,MoairPool,ApiPool,DevopsAnsible,GenericOcr compute
    class MeiliSearch,Grafana,LogServer data
    class GpuEngine gpu
    class Bastion bastion
```

```mermaid
%%{init: {"theme": "base", "themeVariables": {"background": "#ffffff", "primaryColor": "#ffffff"}}}%%
flowchart TD
    subgraph "VPC: NeoX-Production"
        subgraph "NACL: DENY ALL to/from prod, dev, stage subnets"
            subgraph "Testing Subnet"
                %% 测试环境节点
                TestingBastion(["bastion<br/>(c5.4xlarge)"])
                TestingInfra["Core Infra Pool<br/>(6 instances)"]
                TestingAppPool["Scaled App Pool<br/>(4-7 instances)"]

                %% 连接关系
                TestingBastion --> TestingInfra
                TestingBastion --> TestingAppPool
            end
        end
    end

    %% 样式类定义
    classDef bastion fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef compute fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef appPool fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100

    %% 应用样式类
    class TestingBastion bastion
    class TestingInfra compute
    class TestingAppPool appPool
```
