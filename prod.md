```mermaid
graph TD
    subgraph "AWS Cloud"
        subgraph "VPC: NeoX-Production"
            subgraph "Public Subnet"
                Internet[Internet] --> igw[Internet Gateway]
                igw --> bastion("bastion (c5.4xlarge)")
            end
            subgraph "Private Subnet A (Application Tier)"
                recognize_pool("Medical Recognize Pool<br/>(c5.xlarge, c5.2xlarge, c5.4xlarge)")
                moair_pool("Medical Moair Pool<br/>(c5.xlarge)")
                api_pool("Medical API-Only Pool<br/>(c5.xlarge)")
            end
            subgraph "Private Subnet B (Infrastructure & Data Tier)"
                devops_ansible("devops-ansible-01 (c5.xlarge)")
                generic_ocr("generic-ocr-ci-01 (c5.xlarge)")
                meilisearch("medical-meilisearch01 (c5.xlarge)")
                grafana("medical-grafana (c5.xlarge)")
                log_server("medical-pro-generic-log (c5.xlarge)")
            end
            subgraph "Private Subnet C (Specialized Compute)"
                gpu_engine("gpu_engine (g5.xlarge)")
            end
            bastion --> recognize_pool
            bastion --> moair_pool
            bastion --> api_pool
            bastion --> devops_ansible
            bastion --> gpu_engine
            recognize_pool --> gpu_engine
        end
    end
```