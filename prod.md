```mermaid
%%{init: {"theme": "base", "themeVariables": {"background": "#ffffff", "primaryColor": "#ffffff"}}}%%
flowchart TD
    subgraph "AWS Cloud"
        subgraph "VPC: NeoX-Production"
            subgraph "Public Subnet"
                %% 网络入口节点
                Internet([Internet])
                IGW[Internet Gateway]
            end

            subgraph "Private Subnet A (Application Tier)"
                %% 应用服务节点
                RecognizePool["Medical Recognize Pool<br/>(c5.xlarge, c5.2xlarge, c5.4xlarge)"]
                MoairPool["Medical Moair Pool<br/>(c5.xlarge)"]
                ApiPool["Medical API-Only Pool<br/>(c5.xlarge)"]
            end

            subgraph "Private Subnet B (Infrastructure & Data Tier)"
                %% 基础设施节点
                DevopsAnsible["devops-ansible-01<br/>(c5.xlarge)"]
                GenericOcr["generic-ocr-ci-01<br/>(c5.xlarge)"]
                %% 数据服务节点
                MeiliSearch[("medical-meilisearch01<br/>(c5.xlarge)")]
                Grafana[("medical-grafana<br/>(c5.xlarge)")]
                LogServer[("medical-pro-generic-log<br/>(c5.xlarge)")]
            end

            subgraph "Private Subnet C (Specialized Compute)"
                %% GPU计算节点
                GpuEngine["gpu_engine<br/>(g5.xlarge)"]
            end

            subgraph "Bastion Host"
                %% 跳板机节点
                Bastion(["bastion<br/>(c5.4xlarge)"])
            end

            %% 网络连接关系
            Internet --> IGW
            IGW --> Bastion

            %% 跳板机访问关系
            Bastion --> RecognizePool
            Bastion --> MoairPool
            Bastion --> ApiPool
            Bastion --> DevopsAnsible
            Bastion --> GpuEngine

            %% 内部服务关系
            RecognizePool --> GpuEngine
        end
    end

    %% 样式类定义
    classDef network fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef compute fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef data fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef gpu fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef bastion fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c

    %% 应用样式类
    class Internet,IGW network
    class RecognizePool,MoairPool,ApiPool,DevopsAnsible,GenericOcr compute
    class MeiliSearch,Grafana,LogServer data
    class GpuEngine gpu
    class Bastion bastion
```

```mermaid
%%{init: {"theme": "base", "themeVariables": {"background": "#ffffff", "primaryColor": "#ffffff"}}}%%
flowchart TD
    subgraph "VPC: NeoX-Production"
        subgraph "NACL: DENY ALL to/from prod, dev, stage subnets"
            subgraph "Testing Subnet"
                %% 测试环境节点
                TestingBastion(["bastion<br/>(c5.4xlarge)"])
                TestingInfra["Core Infra Pool<br/>(6 instances)"]
                TestingAppPool["Scaled App Pool<br/>(4-7 instances)"]

                %% 连接关系
                TestingBastion --> TestingInfra
                TestingBastion --> TestingAppPool
            end
        end
    end

    %% 样式类定义
    classDef bastion fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef compute fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef appPool fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100

    %% 应用样式类
    class TestingBastion bastion
    class TestingInfra compute
    class TestingAppPool appPool
```

```mermaid
%%{init: {"theme": "base", "themeVariables": {"background": "#ffffff", "primaryColor": "#ffffff"}}}%%
flowchart TD
    subgraph "VPC: NeoX-Production"
        subgraph "NACL: DENY ALL to/from prod, test, stage subnets"
            subgraph "Dev Subnet"
                %% 开发环境节点
                DevServer["medical-dev-01<br/>(m5.xlarge)"]
            end
        end

        subgraph "Production Subnets"
            %% 生产环境节点组
            ProdServers["Production Servers<br/>(详见上方架构图)"]
        end

        subgraph "Testing Subnets"
            %% 测试环境节点组
            TestServers["Testing Servers<br/>(详见上方架构图)"]
        end
    end

    %% 样式类定义
    classDef dev fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef prod fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef test fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100

    %% 应用样式类
    class DevServer dev
    class ProdServers prod
    class TestServers test
```

```mermaid
graph TD
    subgraph "CI/CD Pipeline (AWS CodePipeline)"
        A --> B{Build & Test};
        B --> C{Start Green Env Instances};
        C --> D;
        D --> E{Automated Validation};
        E --> F{Manual Approval};
        F --> G;
        G --> H;
    end

    subgraph "VPC: NeoX-Production"
        subgraph "Traffic Routing (ALB / Route 53)"
            LB("Application Load Balancer")
        end
        subgraph "Blue Environment (Live)"
            Prod_Pool("Production Servers (Running)")
        end
        subgraph "Green Environment (Staging)"
            Staging_Pool("Staging Servers (Stopped/Running)")
        end
        
        Internet --> LB
        LB -- Live Traffic --> Prod_Pool;
        
        G -- "Updates routing rule" --> LB;
        
        style G fill:#f9f,stroke:#333,stroke-width:2px
        
        subgraph "After Traffic Switch"
            LB -- "New Live Traffic" --> Staging_Pool;
            Prod_Pool -- "Becomes idle" --> H;
        end
    end
```mermaid