site_name: NeoX Docs

use_directory_urls: false

nav:
  - 主页: index.md
  - 开发环境搭建:
      - 后端开发:
          - 代码部署: 开发环境搭建/后端开发/代码部署.md
          - AWS ECR权限设置: 开发环境搭建/后端开发/AWS-ECR权限设置.md
          - Docker环境部署: 开发环境搭建/后端开发/Docker环境部署.md
          - 后端代码环境配置: 开发环境搭建/后端开发/后端代码环境配置.md
          - 常见问题解答: 开发环境搭建/后端开发/FAQ.md
  - 自动化运维:
      - 自动化发布平台:
          - Ansible Semaphore: 自动化运维/自动化发布平台/Ansible-Semaphore.md
  - 流程拓扑图:
      - 医疗后端:
          - 识别端流程:
              - 01-生命周期: 流程拓扑图/医疗后端/识别端流程/01-生命周期.md
              - 02-整体架构: 流程拓扑图/医疗后端/识别端流程/02-整体架构.md
              - 03-识别任务的生命周期: 流程拓扑图/医疗后端/识别端流程/03-识别任务的生命周期.md
              - 04-识别任务状态流转: 流程拓扑图/医疗后端/识别端流程/04-识别任务状态流转.md
              - 05-用于结果轮询的Redis缓存: 流程拓扑图/医疗后端/识别端流程/05-用于结果轮询的Redis缓存.md
              - 06-服务分层架构-组件: 流程拓扑图/医疗后端/识别端流程/06-服务分层架构-组件.md
              - 07-识别任务task信息详述: 流程拓扑图/医疗后端/识别端流程/07-识别任务task信息详述.md
              - 08-处方合并流程: 流程拓扑图/医疗后端/识别端流程/08-处方合并流程.md
              - 09-FAX受付流程: 流程拓扑图/医疗后端/识别端流程/09-FAX受付流程.md
          - NSIPS: 流程拓扑图/医疗后端/NSIPS.md
          - GPU 引擎识别流程: 流程拓扑图/医疗后端/GPU引擎识别流程.md
          - Smart Merge: 流程拓扑图/医疗后端/Smart-Merge.md
      - 薬師丸賢太:
          - 处方笺保存匹配流程: 流程拓扑图/薬師丸賢太/处方笺保存匹配流程.md
      - スマート薬局:
          - 薬師丸撫子:
              - 扫描仪连接结果获取流程: 流程拓扑图/スマート薬局/薬師丸撫子/扫描仪连接结果获取流程.md
          # - 薬師丸広司:
          # - 薬師丸鈴音:
          - 通用模块:
              - 日志上传客户端流程: 流程拓扑图/スマート薬局/通用模块/日志上传客户端流程.md
      - 自动化运维:
          - medical-backend: 流程拓扑图/自动化运维/medical-backend.md
          - performance: 流程拓扑图/自动化运维/performance.md
          - terraform: 流程拓扑图/自动化运维/terraform.md

  - 关于:
      - 版本说明: 关于/版本说明/release-notes.md

plugins:
  - mermaid2

theme:
  # name: readthedocs
  # # locale: en
  # # locale: ja
  # locale: zh_CN
  # include_sidebar: true
  name: material
  language: zh
  features:
    - navigation.sections
    - navigation.expand
    - navigation.tabs
    - navigation.top
    - navigation.tracking
    - toc.follow
    - toc.integrate
  palette:
    - scheme: default
      primary: blue
      accent: blue

markdown_extensions:
  - toc:
      permalink: true
      toc_depth: 4
      baselevel: 1
site_description: Docs site for NeoX.
site_author: SongLin Lu
copyright: Copyright © 2025 SongLin Lu. All rights reserved.
