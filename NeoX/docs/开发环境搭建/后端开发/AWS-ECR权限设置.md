# 开启AWS上ECR访问权限

> 请先联系 *赵文瑞* 创建AWS的IAM账号并赋权。

先安装：

[AWS CLI]: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html

操作如下：

```bash
### 配置鉴权 - AWS的IAM账号的ID和Key
aws configure
#  CLI 会提示输入以下信息：
#  AWS Access Key ID: 输入您的访问密钥 ID
#  AWS Secret Access Key: 输入您的访问密钥
#  Default region name: ap-northeast-1
#  Default output format: json

### 创建ECR认证信息
aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-northeast-1.amazonaws.com

### 创建docker用network
docker network create local_medical
``` 