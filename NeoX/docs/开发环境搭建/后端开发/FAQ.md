# 常见问题解答（FAQ）

## 拉起 docker 容器后出现报错的问题

如果执行完 `docker compose` 后，发现好几个` container` 报错，其中基本上就2个问题，`composer` 扩展安装和 `.env` 配置文件的替换。

1. 通过 `docker exec -it local_medical-php-fpm-74-1 bash` 登录容器，在 `/data/www/ `下面 `cd` 对应的项目比如 `neox-med-backend` 和 `bureau-backend`，执行 `composer install` 安装对应的扩展；

2. 找其他同事要下对应项目的本地 `.env` 配置文件。

按上述步骤处理后，重启容器后服务可正常运行。

## API 请求鉴权

要请求 `neox-med-backend` 项目 api，还需要向其他后端同事要下公私钥对，进行鉴权，在 `neox-med-backend` 代码中搜 `JWT_PUBLIC_KEY` 和 `JWT_PRIVATE_KEY`，然后替换成本地的公私钥对所在路径。

可以向其他后端同事要下 postman 的 Collections 文档导出文件进导入，替换下其中的环境变量域名为本地的 `localhost:20201`，就可以进行 api 请求了。

## ERROR: create mountpoint for /etc/ImageMagick-6/policy.xml

启动容器后报错：

```bash
ERROR: for dev-async-recognize-opencv  Cannot start service dev-async-recognize-opencv: failed to create task for container: failed to create shim task: OCI runtime create failed: 
runc create failed: unable to start container process: error during container init: error mounting "/root/neox/medical-docker/services/php/7.4/policy.xml" to rootfs at "/etc/ImageMagick-6/policy.xml": 
create mountpoint for /etc/ImageMagick-6/policy.xml mount: cannot create subdirectories in "/var/lib/docker/overlay2/ecded5dd9166e5b3aef811863c746a048cbf82921dccfb2f621672f9ac2c6577/merged/etc/ImageMagick-6/policy.xml": 
not a directory: unknown: Are you trying to mount a directory onto a file (or vice-versa)? Check if the specified host path exists and is the expected type
```

在 `docker-compose.yaml` 里面把 `/etc/ImageMagick-6/policy.xml` 这个映射去掉。

## Windows 后端环境配置时，拉起 docker 容器时网络连接失败

报错如下：

```bash
b  Unable to connect to deb.debian.org:80: [IP: *************** 80]
E: Unable to fetch some archives, maybe run apt-get update or try with --fix-missing?
```

解决方法：

1. 更换代理

2. 进入容器手动安装

   ```bash
   docker run -it --rm bash
   ```

   进入docker容器后手动输入 `dockerfile` 中的命令安装超时的依赖。

   遇到需要拷贝文件的操作可以copy到docker里面的 `/tmp/` 目录下再进行安装。

   无法执行也可以本地下载后copy过去，比如：

   ```bash
   wget https://raw.githubusercontent.com/php-opencv/php-opencv-packages/master/opencv_4.5.0_amd64.deb
   ```

## 安装 composer 报错

在执行 `composer install` 后报错信息如下：

```bash
Your lock file does not contain a compatible set of packages. Please run composer update.
```

执行如下命令即可解决：

```bash
apt-get update
apt-get install -y libzip-dev

docker-php-ext-configure zip
docker-php-ext-install zip
```

执行上述命令过程中如果出现以下报错：

```bash
fatal: Could not read from remote repository.
```

则在 Bitbucket 的用户设置中的 ssh-keys 中添加新生成的公钥。（https://bitbucket.org/account/settings/ssh-keys/）

生成密钥对的命令：

```bash
ssh-keygen -t rsa -b 2048
```

将生成的密钥对目录下的 `id_rsa.pub` 文件的内容 add 到 key 中。