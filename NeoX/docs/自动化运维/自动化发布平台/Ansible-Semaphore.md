# Ansible Semaphore 自动化运维平台

Ansible Semaphore 自动化发布平台，用于基础设施管理和性能测试。该平台支持多个项目环境，包括医疗后端运维和性能测试场景。

## 功能特性

- **多项目架构**: 为不同运维领域提供有组织的结构
- **基础设施自动化**: 完整的服务器设置和配置管理
- **性能测试框架**: 集成基于 Locust 的负载测试功能
- **安全集成**: 支持 Vault 加密敏感数据
- **Git 钩子集成**: 自动化验证和质量检查，包含 .pem 文件权限管理
- **容器集成**: 基于 Docker 的部署支持，配备 Semaphore UI
- **基础设施即代码**: Terraform 集成用于云资源管理
- **质量保证**: 全面的验证和测试框架

## 平台架构

```
ansible/
├── .githooks/                               # 自动化 Git 钩子
│   ├── post-checkout                       # 自动设置 .pem 文件权限
│   └── post-merge                          # 自动设置 .pem 文件权限
├── env/                                     # 环境配置
│   ├── Binary.md                           # Semaphore 二进制安装指南
│   ├── check_semaphore.sh                  # Semaphore 健康检查脚本
│   ├── docker-compose.yml                  # Docker 部署配置
│   └── semaphore.sh                        # Semaphore 管理脚本
├── projects/                               # 独立项目模块
│   ├── ansible.demo.cfg                    # 演示 Ansible 配置
│   ├── medical-backend/                    # 医疗后端运维
│   │   ├── roles/                          # 医疗专用角色
│   │   ├── inventory/                      # 医疗基础设施清单
│   │   ├── extra-vars/                     # 变量配置
│   │   ├── site.yml                       # 医疗部署剧本
│   │   ├── README.md                      # 医疗后端文档
│   │   └──  USAGE.md                       # 详细使用说明
│   └── performance/                       # 性能测试框架
│       ├── roles/                          # 性能测试角色
│       ├── extra-vars/                     # 测试场景配置
│       ├── inventory/                      # 性能测试清单
│       ├── site.yml                       # 性能测试剧本
│       └── README.md                      # 性能测试文档
├── terraform/                             # 基础设施即代码
│   ├── README.md                          # Terraform 文档
│   └── aws/                               # AWS 专用配置
│       └── windows/                       # Windows EC2 管理
├── test_gathering.yml                     # Ansible 事实收集测试
├── vault-vars.sh                          # Vault 加密工具
└── README.md                              # 本文档
```

## 先决条件

### 系统要求

- Ubuntu/Debian 基础的 Linux 系统
- Python 3.6 或更高版本
- 正确配置的 Git
- Docker 和 Docker Compose（用于 Semaphore UI）
- 对目标服务器的 SSH 访问权限

### 必需工具

1. **Ansible**: 基础设施自动化引擎
2. **Semaphore**: 基于 Web 的 Ansible UI（可选但推荐）
3. **Git**: 版本控制，集成钩子功能
4. **Docker**: 用于 Semaphore 部署的容器运行时
5. **Terraform**: 云资源管理的基础设施即代码工具（可选）

## 安装

### 安装 Ansible

```bash
# 添加 Ansible 仓库
sudo apt-add-repository ppa:ansible/ansible
sudo apt update

# 安装 Ansible
sudo apt install ansible

# 升级 Ansible（需要时）
sudo apt update
sudo apt upgrade ansible

# 验证安装
ansible --version
```

### 安装 Semaphore UI

Semaphore 提供基于 Web 的界面来管理 Ansible 操作。虽然平台支持完整的 Docker 部署，但推荐使用混合方法以获得更好的性能和资源管理。

#### 推荐的安装方法（混合）

为了获得最佳性能，建议仅对数据库层使用容器，并将 Semaphore 作为二进制文件运行：

1. **仅对数据库使用 Docker Compose**: 使用提供的 docker-compose 配置部署 PostgreSQL 数据库
2. **将 Semaphore 作为二进制文件运行**: 使用原生 Semaphore 二进制文件作为 Web 界面和执行引擎

**混合方法的优势：**
- 更好的性能和资源利用率
- 更容易的维护和更新
- 更灵活的配置选项
- 减少容器开销

👉 **[二进制安装指南](https://bitbucket.org/neoxinc/ansible/src/main/env/Binary.md)** - 二进制部署的完整设置说明

#### 完整 Docker 部署（替代方案）

如果您更喜欢完全容器化的设置，可以使用完整的 Docker Compose 配置：

```bash
# 创建自定义 docker 网络
sudo docker network create ${YOUR_NETWORK_NAME}

# 验证配置
sudo docker-compose config
# 或使用环境文件
sudo docker-compose --env-file .env config

# 启动 Semaphore（完整堆栈）
sudo docker-compose up -d
```

配置详情请参见：[docker-compose.yml](https://bitbucket.org/neoxinc/ansible/src/main/env/docker-compose.yml)

#### 仅数据库 Docker 部署（推荐）

对于混合方法，仅启动数据库服务：

```bash
# 创建自定义 docker 网络
sudo docker network create ${YOUR_NETWORK_NAME}

# 仅启动 PostgreSQL 数据库
sudo docker-compose up -d semaphore_db

# 验证数据库正在运行
sudo docker-compose ps semaphore_db
```

然后按照[二进制安装指南](https://bitbucket.org/neoxinc/ansible/src/main/env/Binary.md)使用容器化数据库设置 Semaphore 二进制文件。

## 配置

### Git 配置设置

确保 Git 钩子和文件权限的正确配置：

```bash
# 检查并设置 git filemode
git config --get core.filemode
# 如果不是 true，则设置它
git config core.filemode true

# 检查并设置 git hooks 路径
git config --get core.hooksPath
# 如果不是 .githooks，则设置它
git config core.hooksPath .githooks
```

**Git 钩子功能：**
- **自动 .pem 文件权限管理**: Post-checkout 和 post-merge 钩子自动为 SSH 密钥文件设置正确权限（600）
- **安全增强**: 确保 SSH 密钥在 Git 操作后得到适当保护
- **无缝集成**: 与正常的 Git 工作流程透明地工作

### Vault 配置

对于使用加密变量的项目，根据需要配置 vault 密码文件。
