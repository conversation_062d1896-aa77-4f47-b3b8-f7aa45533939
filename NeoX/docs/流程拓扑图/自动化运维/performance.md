# Performance Testing Infrastructure Workflow

## 项目概述

Performance Testing Infrastructure 是一个基于 Ansible 和 Locust 的综合性能测试框架，提供自动化测试环境部署、分布式负载测试能力、性能指标收集和分析等功能。

## 工作流程图

```mermaid
flowchart TD
    Start([开始执行 Performance Testing]) --> CheckPhase{选择执行阶段}
    
    %% Phase 1: 基础设施准备
    CheckPhase -->|play_prepare 或 deploy_all| Phase1[Phase 1: 基础设施准备]
    Phase1 --> UpdateSystem[更新系统包<br/>apt update & upgrade]
    UpdateSystem --> InstallDocker[安装 Docker 和依赖<br/>配置 Docker 仓库<br/>安装 Docker Engine]
    InstallDocker --> InstallPip[安装 Python3 pip<br/>安装必要的 Python 包]
    InstallPip --> InstallConda[安装 Miniconda3<br/>配置 Conda 环境]
    InstallConda --> Phase1Complete[Phase 1 完成]
    
    %% Phase 2: 生成器服务部署
    CheckPhase -->|play_generator 或 deploy_all| Phase2[Phase 2: 生成器服务部署]
    Phase1Complete --> Phase2
    Phase2 --> CheckConda{检查 Conda 是否已安装}
    CheckConda -->|未安装| InstallCondaGen[安装 Miniconda3]
    CheckConda -->|已安装| ConfigGenerator[配置生成器环境]
    InstallCondaGen --> ConfigGenerator
    ConfigGenerator --> BuildLocust[构建 Locust Docker 镜像<br/>检查镜像是否存在<br/>执行 BuildImage.sh]
    BuildLocust --> Phase2Complete[Phase 2 完成]
    
    %% Phase 3: Locust 性能测试执行
    CheckPhase -->|locust_run| Phase3[Phase 3: Locust 性能测试执行]
    Phase2Complete --> Phase3
    Phase3 --> SetupDeps3[设置依赖<br/>SetFacts, RepoOps, Generator]
    SetupDeps3 --> CheckRunType{检查运行类型}
    
    %% Start 分支
    CheckRunType -->|run_type=start| StartBranch[开始测试流程]
    StartBranch --> CheckSyncData{是否同步数据?<br/>standalone.sync_data}
    CheckSyncData -->|true| SyncData[同步项目数据<br/>从仓库复制测试场景文件]
    CheckSyncData -->|false| CheckBuildImage{是否重建镜像?<br/>standalone.build_image}
    SyncData --> CheckBuildImage
    CheckBuildImage -->|true| BuildImage[重建 Locust Docker 镜像<br/>执行 BuildImage.sh]
    CheckBuildImage -->|false| StartContainers[启动 Locust 容器<br/>docker-compose up -d]
    BuildImage --> StartContainers
    StartContainers --> CheckDebug{是否启用调试?<br/>standalone.debug}
    CheckDebug -->|true| ShowLogs[显示容器实时日志<br/>docker-compose logs -f]
    CheckDebug -->|false| TestRunning[测试运行中...]
    ShowLogs --> TestRunning
    
    %% Stop 分支
    CheckRunType -->|run_type=stop| StopBranch[停止测试流程]
    StopBranch --> StopContainers[停止 Locust 容器<br/>docker-compose down]
    StopContainers --> CleanupResources[清理资源]
    
    %% Backup 分支
    CheckRunType -->|run_type=backup| BackupBranch[备份测试结果]
    BackupBranch --> CreateBackup[创建结果备份<br/>压缩测试结果文件]
    CreateBackup --> ArchiveResults[归档到备份目录]
    
    %% 测试模式选择
    TestRunning --> CheckTestMode{选择测试模式}
    CheckTestMode -->|standalone| StandaloneTest[独立模式测试<br/>单节点 Locust 执行]
    CheckTestMode -->|distributed| DistributedTest[分布式模式测试<br/>多节点协调执行<br/>（待实现）]
    
    StandaloneTest --> MonitorTest[监控测试执行<br/>收集性能指标]
    DistributedTest --> MonitorTest
    MonitorTest --> TestComplete[测试执行完成]
    
    %% 汇聚点
    CleanupResources --> Complete[任务完成]
    ArchiveResults --> Complete
    TestComplete --> Complete
    Complete --> End([执行结束])
    
    %% 使用说明分支
    CheckPhase -->|usage| ShowUsage[显示使用说明<br/>可用标签和示例命令]
    ShowUsage --> End
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef phase fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef process fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef test fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef backup fill:#fce4ec,stroke:#ad1457,stroke-width:2px
    
    class Start,End startEnd
    class Phase1,Phase2,Phase3 phase
    class UpdateSystem,InstallDocker,InstallPip,InstallConda,InstallCondaGen,ConfigGenerator,BuildLocust,SetupDeps3,SyncData,BuildImage,StartContainers,ShowLogs,StopContainers,CleanupResources,MonitorTest process
    class CheckPhase,CheckConda,CheckRunType,CheckSyncData,CheckBuildImage,CheckDebug,CheckTestMode decision
    class StandaloneTest,DistributedTest test
    class CreateBackup,ArchiveResults backup
```

## 主要组件说明

### 阶段划分 (Phases)
- **Phase 1 (01-prepare)**: 基础设施准备，安装系统依赖
- **Phase 2 (02-generator)**: 生成器服务部署，配置测试环境
- **Phase 3 (03-locust)**: Locust 性能测试执行

### 依赖角色 (Dependencies)
- **Dep.SetFacts**: 设置性能测试相关的全局变量
- **Dep.SshConnection**: 管理SSH连接配置
- **Dep.RepoOps**: 处理测试仓库同步

### 测试配置
- **Standalone Mode**: 单节点独立测试模式
- **Distributed Mode**: 分布式多节点测试模式（待实现）
- **Run Types**: start（开始）、stop（停止）、backup（备份）

## 使用示例

### 完整环境部署
```bash
ansible-playbook site.yml -t deploy_all -e "@extra-vars/locust/locust-standalone-sde.yml"
```

### 启动性能测试
```bash
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=start"
```

### 启动测试并启用调试
```bash
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-pres.yml" -e "run_type=start" -e "standalone.debug=true"
```

### 停止运行中的测试
```bash
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=stop"
```

### 备份测试结果
```bash
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-pres.yml" -e "run_type=backup"
```

## 关键特性

- **自动化测试环境部署**: 一键部署完整的性能测试环境
- **分布式负载测试能力**: 支持多节点协调的大规模测试
- **性能指标收集和分析**: 集成监控和结果分析
- **多种测试场景支持**: 支持不同的测试场景配置
- **Conda 环境管理**: 自动管理测试工具的依赖环境
- **可扩展架构**: 支持生成器节点的横向扩展
