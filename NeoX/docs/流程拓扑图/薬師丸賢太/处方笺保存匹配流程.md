```mermaid
flowchart TD
    Start(["开始
    SearchByPrescriptionInfo"]) --> InitialFilter["初步筛
    InitialFilter"]
    InitialFilter -->|"生日匹配?"| BirthdayMatch{生日匹配?}
    BirthdayMatch -- 是 --> CandidateAdd1["加入候选"]
    BirthdayMatch -- 否 --> NameRegexMatch{姓名正则匹配?}
    NameRegexMatch -- 是 --> CandidateAdd1
    NameRegexMatch -- 否 --> Skip1["跳过"]
    CandidateAdd1 --> DeliveryDateCheck{交付日期有值?}
    DeliveryDateCheck -- 有且匹配 --> CandidateAdd2["加入候选"]
    DeliveryDateCheck -- 无或不匹配 --> Skip2["跳过"]
    Skip1 --> Next1["下一个"]
    CandidateAdd2 --> Next1
    Skip2 --> Next1
    Next1 --> PatientFilter["患者过滤
    FilterByPatient"]
    PatientFilter --> PatientCheck{生日和姓名都不匹配?}
    PatientCheck -- 是 --> Remove1["移除"]
    PatientCheck -- 否 --> Keep1["保留"]
    Remove1 --> HospitalFilter["医院编码过滤
    FilterByHospitalCode"]
    Keep1 --> HospitalFilter
    HospitalFilter --> HospitalCodeMatch{医院编码匹配?}
    HospitalCodeMatch -- 是 --> Keep2["保留"]
    HospitalCodeMatch -- 否 --> Remove2["移除"]
    Keep2 --> DepartDoctorFilter["科室和医生过滤
    FilterByDepartDoctor"]
    Remove2 --> DepartDoctorFilter
    DepartDoctorFilter --> DepartDoctorMatch{科室或医生匹配?}
    DepartDoctorMatch -- 是 --> Keep3["保留"]
    DepartDoctorMatch -- 否 --> Remove3["移除"]
    Keep3 --> PEFilter["PE信息过滤
    FilterByPE"]
    Remove3 --> PEFilter
    PEFilter --> PEMatch{PE信息匹配?}
    PEMatch -- 是 --> Keep4["保留"]
    PEMatch -- 否 --> Remove4["移除"]
    Keep4 --> ReturnResult["返回候选"]
    Remove4 --> ReturnResult
    ReturnResult --> End(["结束
    返回匹配结果"])

    %% 细节说明
    subgraph "姓名正则与模糊匹配细节"
      NameRegexMatch -.-> RegexStep1["正则匹配<br/>IsNameMatchWithRegex"]
      RegexStep1 -.-> FuzzyStep["字符分割 OR 匹配"]
      FuzzyStep -.-> Fallback["失败回退: 简单包含判断"]
    end

    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef candidate fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef skip fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef detail fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#33691e

    %% 应用样式类
    class Start,End startEnd
    class InitialFilter,PatientFilter,DoctorFilter,DeliveryFilter process
    class BirthdayMatch,NameRegexMatch,DeliveryDateCheck,PatientCheck,DoctorCheck,DeliveryCheck decision
    class CandidateAdd1,CandidateAdd2,Keep1,Keep2,Keep3,Keep4,ReturnResult candidate
    class Skip1,Skip2,Remove1,Remove2,Remove3,Remove4,Next1,Next2,Next3,Next4 skip
    class RegexStep1,FuzzyStep,Fallback detail
```
