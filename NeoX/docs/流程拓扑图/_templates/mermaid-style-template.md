# Mermaid 样式模板和标准

## 统一样式模板

### 基础模板
```mermaid
flowchart TD
    %% 节点定义
    Start([开始])
    Process[处理步骤]
    Decision{决策点}
    Database[(数据库)]
    Cache[缓存操作]
    Output([输出结果])
    
    %% 连接关系
    Start --> Process
    Process --> Decision
    Decision -->|是| Database
    Decision -->|否| Cache
    Database --> Output
    Cache --> Output
    
    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef database fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef cache fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef output fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32
    
    %% 应用样式类
    class Start,Output startEnd
    class Process process
    class Decision decision
    class Database database
    class Cache cache
```

## 颜色方案标准

### 主要颜色定义
- **开始/结束**: `#1565c0` (深蓝色)
- **处理步骤**: `#6a1b9a` (紫色)
- **决策点**: `#ef6c00` (橙色)
- **数据库**: `#2e7d32` (绿色)
- **缓存/Redis**: `#c62828` (红色)
- **错误/失败**: `#b71c1c` (深红色)

### 背景色方案
- **开始/结束**: `#e3f2fd` (浅蓝色背景)
- **处理步骤**: `#f3e5f5` (浅紫色背景)
- **决策点**: `#fff3e0` (浅橙色背景)
- **数据库**: `#e8f5e8` (浅绿色背景)
- **缓存/Redis**: `#ffebee` (浅红色背景)

## 语法现代化标准

### 推荐语法
1. 使用 `flowchart TD` 替代 `graph TD`
2. 使用 `classDef` 定义样式类
3. 使用 `class` 应用样式类
4. 使用 `subgraph` 进行逻辑分组

### 节点形状规范
- `([])` - 开始/结束节点
- `[]` - 处理步骤
- `{}` - 决策点
- `[()]` - 数据库
- `[[]]` - 子程序
- `[//]` - 输入/输出

## 示例：完整的流程图模板

```mermaid
flowchart TD
    subgraph "客户端层"
        A([用户请求])
        B[参数验证]
    end
    
    subgraph "业务处理层"
        C{业务逻辑判断}
        D[数据处理]
        E[结果计算]
    end
    
    subgraph "数据层"
        F[(主数据库)]
        G[Redis缓存]
    end
    
    A --> B
    B --> C
    C -->|通过| D
    C -->|失败| H([错误返回])
    D --> E
    E --> F
    E --> G
    F --> I([成功返回])
    G --> I
    
    %% 样式定义
    classDef client fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef business fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef data fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef cache fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef result fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32
    classDef error fill:#ffcdd2,stroke:#b71c1c,stroke-width:2px,color:#b71c1c
    
    class A,I client
    class B,D,E business
    class C decision
    class F data
    class G cache
    class H error
```

## 优化检查清单

### 语法检查
- [ ] 使用现代 `flowchart` 语法
- [ ] 正确使用 `subgraph` 分组
- [ ] 使用 `classDef` 定义样式类
- [ ] 节点命名清晰且有意义

### 样式检查
- [ ] 颜色方案符合标准
- [ ] 节点形状使用规范
- [ ] 样式类应用正确
- [ ] 整体视觉效果协调

### 内容检查
- [ ] 流程逻辑清晰
- [ ] 中文标签正确显示
- [ ] 连接关系准确
- [ ] 分组逻辑合理
