```mermaid
flowchart TD
    subgraph "客户端请求阶段"
        A([客户端发起识别请求]) --> B["apply()<br/>创建Task占位<br/>返回taskId"]
        B --> C["upload()<br/>上传处方图片<br/>Task进入待识别队列"]
    end

    subgraph "任务调度与识别阶段"
        C --> D["PrescriptionAsyncDispatcher<br/>任务调度器<br/>分配QPS和识别进程"]
        D --> E["PrescriptionAsyncRecognize<br/>识别进程<br/>调用identify()进行OCR/QR识别"]
        E --> F["PrescriptionAsyncMerge<br/>合并进程<br/>判断是否需要合并多页处方"]
    end

    subgraph "结果处理阶段"
        F --> G["finishPrescription()<br/>处方入库<br/>结果写入Redis"]
        G --> H["客户端轮询<br/>qrListIds()<br/>获取识别结果"]
        H --> I{获取到结果?}
        I -->|是| J["qrNoticeDownloaded()<br/>确认下载完成<br/>清理Redis数据"]
        I -->|否| K[等待间隔]
        K --> H
        J --> L([识别完成])
    end

    subgraph "循环控制"
        L --> M[等待下一次apply]
        M --> A
    end

    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef clientOp fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef scheduler fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef processor fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef polling fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef control fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#33691e

    %% 应用样式类
    class A,L startEnd
    class B,C clientOp
    class D scheduler
    class E,F,G processor
    class H,J polling
    class I decision
    class K,M control
```
