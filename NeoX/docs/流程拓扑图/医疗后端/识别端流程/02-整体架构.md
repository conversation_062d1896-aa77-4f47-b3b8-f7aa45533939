```mermaid
flowchart TD
    subgraph "Client 客户端层"
        A1[发起apply请求]
        A2[上传图片文件]
        A3[轮询qrListIds]
        A4[调用qrNoticeDownloaded]
    end

    subgraph "API Controller 控制器层"
        B1[PrescriptionAsyncController::apply]
        B2[PrescriptionAsyncController::uploadFile]
        B3[PrescriptionAsyncController::qrListIds]
        B4[PrescriptionAsyncController::qrNoticeDownloaded]
    end

    subgraph "Task Management 任务管理层"
        C1[PTask::create<br/>创建任务记录]
        C2[更新任务状态<br/>WAIT_UPLOAD → WAIT_RECOGNIZE]
        C3[任务调度<br/>PrescriptionAsyncDispatcher]
        C4[临时表<br/>p_task_temp]
    end

    subgraph "Recognition 识别处理层"
        D1[PrescriptionAsyncRecognize<br/>识别进程]
        D2[PrescriptionTrait::identify<br/>OCR/QR识别]
        D3[Selector引擎<br/>文字识别]
        D4[JahisParser<br/>QR码解析]
    end

    subgraph "Merge & Finish 合并完成层"
        E1[PrescriptionAsyncMerge<br/>合并进程]
        E2[mergeTaskList<br/>处方合并逻辑]
        E3[finishPrescription<br/>处方完成]
        E4[PrescriptionOriginal::store<br/>处方入库]
    end

    subgraph "Redis Cache 缓存层"
        F1[任务状态缓存<br/>QR_LIST_STATUS_*]
        F2[识别结果缓存<br/>PrescriptionAsyncRecognizedMsg]
        F3[轮询时间戳<br/>FetchStatusTimeStamp]
    end

    subgraph "Database 数据库层"
        G1[(p_task<br/>处方识别任务表)]
        G2[(p_task_failed<br/>失败任务表)]
        G3[(prescription_original<br/>处方结果表)]
        G4[(p_task_temp<br/>临时任务表)]
    end

    subgraph "QPS & Process 资源管理层"
        H1[QPS锁管理<br/>控制识别频率]
        H2[ProcessPool<br/>进程池管理]
    end

    %% 客户端到控制器层连接
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4

    %% 控制器层到任务管理层连接
    B1 --> C1
    B2 --> C2
    B3 --> F1
    B3 --> F2
    B3 --> F3
    B4 --> F2

    %% 任务管理层连接
    C1 --> G1
    C2 --> G1
    C3 --> H1
    C3 --> H2
    C3 --> C4
    C4 --> G4
    C4 --> D1

    %% 识别处理层连接
    D1 --> D2
    D2 --> D3
    D2 --> D4
    D1 --> G1
    D1 --> G2

    %% 合并完成层连接
    G1 --> E1
    E1 --> E2
    E1 --> G2
    E2 --> E3
    E3 --> E4
    E4 --> G3
    E3 --> F1
    E3 --> F2

    %% 样式类定义
    classDef client fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef controller fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef taskMgmt fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef recognition fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef merge fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32
    classDef cache fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef database fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef failedDb fill:#ffcdd2,stroke:#b71c1c,stroke-width:2px,color:#b71c1c
    classDef resource fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#33691e

    %% 应用样式类
    class A1,A2,A3,A4 client
    class B1,B2,B3,B4 controller
    class C1,C2,C3,C4 taskMgmt
    class D1,D2,D3,D4 recognition
    class E1,E2,E3,E4 merge
    class F1,F2,F3 cache
    class G1,G3,G4 database
    class G2 failedDb
    class H1,H2 resource
```
