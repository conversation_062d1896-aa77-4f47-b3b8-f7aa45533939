```mermaid
flowchart TD
    subgraph "Redis Data Structure Redis数据结构"
        R1["PrescriptionAsyncRecognizedMsgPC:{merchantId}<br/>PC端识别结果消息"]
        R2["PrescriptionAsyncRecognizedMsgAPI:{merchantId}<br/>API端识别结果消息"]
        R3["PrescriptionAsyncNum:{merchantId}<br/>消息计数"]
        R4["CleanMerchantRecognizeResult:{merchantId}<br/>清理锁"]
        R5["Task Status Cache<br/>任务状态缓存"]
    end

    subgraph "Client Polling 客户端轮询"
        C1[客户端启动轮询<br/>FetchStatusTimeStamp=0]
        C2[调用qrListIds<br/>传入timestamp]
        C3[Redis按时间戳查询<br/>getRedisTaskByTimestamp]
        C4[返回结果<br/>qrList, merge, finish]
        C5[更新时间戳<br/>lastTimestamp += 1]
        C6[客户端处理结果<br/>显示给用户]
        C7[确认下载<br/>qrNoticeDownloaded]
    end

    subgraph "Result Processing 结果处理"
        P1{识别状态判断}
        P2[QR_LIST_STATUS_SUCCESS<br/>成功完成]
        P3[QR_LIST_STATUS_QR<br/>QR识别完成]
        P4[QR_LIST_STATUS_DOING<br/>处理中]
        P5[QR_LIST_STATUS_FAILED<br/>识别失败]
        P6[QR_LIST_STATUS_MERGED<br/>已合并]
    end

    subgraph "Cleanup Logic 清理逻辑"
        L1[检查过期时间<br/>超过4天的结果]
        L2[多设备店铺<br/>仅删除过期结果]
        L3[单设备店铺<br/>删除已确认+过期结果]
        L4[deleteRedisTaskByIds<br/>批量删除]
    end

    C1 --> C2
    C2 --> C3
    C3 --> R5
    R5 --> P1
    P1 --> P2
    P1 --> P3
    P1 --> P4
    P1 --> P5
    P1 --> P6
    P2 --> C4
    P3 --> C4
    P4 --> C2
    P5 --> C4
    P6 --> C4
    C4 --> C5
    C5 --> C6
    C6 --> C7
    C7 --> L1
    L1 --> L2
    L1 --> L3
    L2 --> L4
    L3 --> L4
    L4 --> R1
    L4 --> R2

    %% 样式类定义
    classDef startEnd fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef process fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef database fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef cache fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef success fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32
    classDef error fill:#ffcdd2,stroke:#b71c1c,stroke-width:2px,color:#b71c1c
    classDef client fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0

    %% 应用样式类
    class R1,R2,R3,R4,R5 cache
    class C1,C6 client
    class C2,C5,C7 process
    class C3,C4,L4 cache
    class P1 decision
    class P2,P3 success
    class P4,P6 process
    class P5 error
    class L1,L2,L3 process
```
