```mermaid
flowchart TD
    subgraph "Task Status Flow 任务状态流转"
        A[TASK_STATUS_WAIT_UPLOAD<br/>等待上传]
        B[TASK_STATUS_WAIT_RECOGNIZE<br/>等待识别]
        C[TASK_STATUS_DISPATCHED<br/>已分发]
        D[TASK_STATUS_RECOGNIZING<br/>识别中]
        E[TASK_STATUS_RECOGNIZED<br/>识别完成]
        F[TASK_STATUS_MERGED<br/>已合并]
        G[TASK_STATUS_VIEWED<br/>已查看]
        H[TASK_STATUS_WORKED<br/>已处理]

        FAIL1[TASK_STATUS_FAILED_CONVERT_IMG<br/>图片转换失败]
        FAIL2[TASK_STATUS_RECOGNIZE_FAILED<br/>识别失败]
        FAIL3[TASK_STATUS_ENQUETE<br/>问卷调查]
    end

    subgraph "Redis Status Redis状态"
        R1[QR_LIST_STATUS_DOING<br/>处理中]
        R2[QR_LIST_STATUS_SUCCESS<br/>成功]
        R3[QR_LIST_STATUS_QR<br/>QR识别]
        R4[QR_LIST_STATUS_FAILED<br/>失败]
        R5[QR_LIST_STATUS_MERGED<br/>已合并]
    end

    subgraph "Process Events 处理事件"
        P1["apply()"]
        P2["uploadFile()"]
        P3["Dispatcher分发"]
        P4["Recognition识别"]
        P5["Merge合并"]
        P6["finishPrescription()"]
        P7["客户端查看"]
        P8["客户端处理"]
    end

    %% 主流程连接
    P1 --> A --> P2 --> B --> P3 --> C --> P4 --> D --> E
    E --> P5 --> F --> P6 --> G --> P7 --> H --> P8

    %% 失败分支
    P2 --> FAIL1
    P4 --> FAIL2
    P2 --> FAIL3

    %% Redis状态映射
    A --> R1
    E --> R2
    E --> R3
    FAIL1 --> R4
    FAIL2 --> R4
    F --> R5

    %% 样式类定义
    classDef waiting fill:#e3f2fd,stroke:#1565c0,stroke-width:2px,color:#1565c0
    classDef processing fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#e65100
    classDef completed fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
    classDef failed fill:#ffcdd2,stroke:#b71c1c,stroke-width:2px,color:#b71c1c
    classDef redis fill:#ffebee,stroke:#c62828,stroke-width:2px,color:#b71c1c
    classDef events fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px,color:#4a148c

    %% 应用样式类
    class A,B waiting
    class C,D,E processing
    class F,G,H completed
    class FAIL1,FAIL2,FAIL3 failed
    class R1,R2,R3,R4,R5 redis
    class P1,P2,P3,P4,P5,P6,P7,P8 events
```
